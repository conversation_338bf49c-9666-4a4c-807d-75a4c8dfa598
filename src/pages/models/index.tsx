import React, { useState, useMemo, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { CommonNavBar } from '@/components/ui/CommonNavBar'
import { useTranslation } from 'react-i18next'
import { ThemeDetail } from '@/apis/types'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import useSWR from 'swr'
import { MirrorLoading } from 'wujieai-react-icon'
import MazeSingleTemplateList from '@/components/pages/homepage/MazeSingleTemplateList'
import { CreateBtn } from '@/components/pages/homepage/CreateBtn'
import { useBridge } from '@/hooks/useBridge'

/**
 * 模型详情页面
 * 显示当前模型包含的主题列表
 */
const ModelDetail: React.FC = () => {
  const [searchParams] = useSearchParams()
  const { t } = useTranslation()
  const { tabbarVisible } = useBridge()

  useEffect(() => {
    tabbarVisible({ visible: 0 })
  }, [])

  // 从URL参数获取模型信息
  const modelId = searchParams.get('id')

  // 当前选中的模板
  const [activeTemplate, setActiveTemplate] = useState<
    ThemeDetail | null | undefined
  >(null)

  // 获取主题详情数据
  const { data: themeDetailData, isLoading } = useSWR(
    modelId ? [modelId] : null,
    ([id]) => _ajax.get(_api.theme_detail, { params: { id } })
  )

  // 过滤并处理主题列表数据，转换为ThemeDetail格式
  const detailList = useMemo(() => {
    const itemList = themeDetailData?.data?.data?.itemList || []
    // 将itemList转换为ThemeDetail格式以兼容MazeSingleTemplateList组件
    return itemList.map((item: any) => ({
      id: item.id,
      name: item.name,
      cover_image: item.image,
      cover_image_female: item.image,
      template_count: 1,
      type: 0,
      price: item.price || 0,
      video: {
        resultUrl: '',
      },
    })) as ThemeDetail[]
  }, [themeDetailData])

  // 设置默认选中第一个
  useEffect(() => {
    if (detailList.length > 0) {
      setActiveTemplate(detailList[0])
    }
  }, [detailList])

  return (
    <div className="w-full h-full flex flex-col">
      {/* 通用导航栏 */}
      <CommonNavBar title={themeDetailData?.data?.data?.name} />

      {/* 页面内容区域 */}
      <div className="flex-1 relative">
        {isLoading && (
          <div className="flex items-center justify-center w-full h-full">
            <MirrorLoading className="animate-spin maze-primary-text w-12 h-12" />
          </div>
        )}
        {!isLoading && detailList.length === 0 && (
          <div className="flex items-center justify-center w-full h-full">
            <div className="text-center text-white">
              <div className="text-xl font-bold opacity-65">
                {t('当前分类下还没有模板')}
              </div>
            </div>
          </div>
        )}

        {!isLoading && detailList.length > 0 && (
          <MazeSingleTemplateList
            selectTemplateList={detailList}
            activeTemplate={activeTemplate}
            setActiveTemplate={setActiveTemplate}
            listKey={`model-detail-${modelId}`}
            multiline={false}
          />
        )}
      </div>
      <div className="px-6 py-2 flex gap-2">
        <p className="flex-1 text-ellipsis overflow-hidden text-white font-[14px]">
          {themeDetailData?.data?.data?.desc}
          啊大姐夫卡蒂狗hi啊电极法卡代发i啊登记规划地方阿德阿凡达噶打暑假工卡卡关卡卡
        </p>
        <span>More</span>
      </div>
      {/* 底部创建按钮 */}
      <div className="p-[16px]">
        <CreateBtn selectTemplateList={detailList} />
      </div>
    </div>
  )
}

export default ModelDetail
