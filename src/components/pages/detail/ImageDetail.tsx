import { use<PERSON>tom } from 'jotai'
import { PrinterStatus } from '@/stores/types'
import { VirtualInfo } from '@/apis/types'
import { useNavigate } from 'react-router-dom'

import {
  resultOrderAtom,
  printerEnableAtom,
  machineInfoAtom,
  selectedEventIdAtom,
  selectedEventDetailAtom,
} from '@/stores'
import { useSearchParams } from 'react-router-dom'
import { useEffect, useState, useMemo } from 'react'
import { useAiTask } from '@/components/pages/photo/useAiTask'
import { useDevice } from '@/hooks/useDevice'
import { useTranslation } from 'react-i18next'
import { SendToEmailModal } from './SendToEmailModal'
import { DownloadModal } from './DownloadModal'
import { ShareModal } from './ShareModal'
import { PrintModal } from './PrintModal'
import { MyMirrorAiTask } from '@/stores/types'
import { AiTaskDetailType } from '@/graphqls/types'

import { publicPreLoadSourceObj } from '@/configs/source'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import classNames from 'classnames'
import { useBridge } from '@/hooks/useBridge'
import { toast } from '@/components/ui/shad/use-toast'
import { SvgIcon } from '@/components/ui/SvgIcon'
import { MainImage } from '../result/MainImage'
import { CommonNavBar } from '@/components/ui/CommonNavBar'
import { Button } from '@/components/ui/shad/button'

export const ImageDetail = () => {
  const [curVirtualInfo, setCurVirtualInfo] = useState<VirtualInfo>()
  const [, setResultOrder] = useAtom(resultOrderAtom)
  const [selectedEventId] = useAtom(selectedEventIdAtom)
  const [selectedEventDetail] = useAtom(selectedEventDetailAtom)
  const { getDefaultDeviceInfo } = useDevice()
  const { downImage, tabbarVisible, tabbarTo } = useBridge()
  const { t } = useTranslation()
  const navigate = useNavigate()

  const [curOptType, setCurOptType] = useState<string | null>(null)
  const [activeImageIndex] = useState(0)
  // Store all selected images with their taskBaseIds
  const [selectedImageData, setSelectedImageData] = useState<{
    taskBaseIds: number[]
    imageIds: { [taskBaseId: number]: number[] }
    images: MyMirrorAiTask[]
  }>({
    taskBaseIds: [],
    imageIds: {},
    images: [],
  })

  const [searchParams] = useSearchParams()
  const [printerEnable] = useAtom(printerEnableAtom)
  const [machineInfo] = useAtom(machineInfoAtom)

  useEffect(() => {
    tabbarVisible({ visible: 0 })
  }, [])
  // Current active image
  const curImg = useMemo(() => {
    return selectedImageData.images[activeImageIndex] || null
  }, [selectedImageData.images, activeImageIndex])
  const curVideo = curImg?.video

  const hasPrinter = useMemo(() => {
    return (
      printerEnable &&
      (machineInfo?.printers?.some(
        it => it.printerStatus === PrinterStatus.AVAILABLE
      ) ||
        machineInfo?.printerStatus === PrinterStatus.AVAILABLE)
    )
  }, [printerEnable, machineInfo])
  // Format: /detail?images=taskId1:imageId1,imageId2;taskId2:imageId3,imageId4
  const imagesParam = searchParams.get('images')

  // Parse the images parameter and fetch the images
  useEffect(() => {
    if (!imagesParam) return

    try {
      // Parse the images parameter
      // Format: taskId1:imageId1,imageId2;taskId2:imageId3,imageId4
      const taskImageMap: { [taskBaseId: number]: number[] } = {}
      const taskBaseIds: number[] = []

      imagesParam.split(';').forEach(taskImagePair => {
        const [taskId, imageIdsStr] = taskImagePair.split(':')
        const taskBaseId = Number(taskId)
        const imageIds = imageIdsStr.split(',').map(Number)

        taskImageMap[taskBaseId] = imageIds
        taskBaseIds.push(taskBaseId)
      })

      setSelectedImageData(prev => ({
        ...prev,
        taskBaseIds,
        imageIds: taskImageMap,
      }))

      // Stop any existing polling before starting new ones
      stopPollAiTaskStatus()

      // Create a single polling function that handles all taskBaseIds
      const pollAllTasks = () => {
        // Use Promise.all to fetch all tasks in parallel
        Promise.all(
          taskBaseIds.map(
            taskBaseId =>
              new Promise(resolve => {
                pollAiTaskStatus({
                  taskBaseId,
                  onProgress({ taskList }) {
                    updateImagesForTask(taskBaseId, taskList)
                    resolve(null)
                  },
                  onSuccess({ order, taskList }) {
                    updateImagesForTask(taskBaseId, taskList)
                    setResultOrder(order)
                    resolve(null)
                  },
                  onFail() {
                    console.error(
                      `Failed to fetch images for taskBaseId: ${taskBaseId}`
                    )
                    resolve(null)
                  },
                })
              })
          )
        ).catch(err => {
          console.error('Error polling tasks:', err)
        })
      }

      // Start polling
      pollAllTasks()
    } catch (error) {
      console.error('Error parsing images parameter:', error)
    }
  }, [imagesParam])

  // Update the images for a specific taskBaseId
  const updateImagesForTask = (
    taskBaseId: number,
    taskList: MyMirrorAiTask[]
  ) => {
    console.log(`=== 更新任务 ${taskBaseId} 的图片 ===`)
    console.log(
      '接收到的taskList:',
      taskList.map(t => ({ id: t.id, type: t.detailType }))
    )

    setSelectedImageData(prev => {
      // Filter the task list to only include DRAW type results
      const drawImages = taskList.filter(
        img => img.detailType === AiTaskDetailType.DRAW
      )
      // Get video results for later use
      const videoImages = taskList.filter(
        img => img.detailType === AiTaskDetailType.VIDEO
      )
      const resultImages = drawImages?.map(item => ({
        ...item,
        video: videoImages?.[0],
      }))

      return {
        ...prev,
        images: [...prev.images, ...resultImages],
      }
    })
  }

  console.log(
    'result',
    hasPrinter,
    printerEnable,
    machineInfo,
    curImg,
    selectedEventDetail,
    selectedImageData
  )

  const { pollAiTaskStatus, stopPollAiTaskStatus } = useAiTask()

  const handleToolbarClick = (op: string) => {
    if (op === 'download') {
      downImage({
        imageUrls: [curImg?.resultUrl],
      })
      return
    }
    if (op === 'print') {
      _ajax.post(_api.record_print, { event_id: selectedEventId })
    }
    setCurOptType(op)
  }

  useEffect(() => {
    getVirtualDevice()
  }, [])

  const getVirtualDevice = async () => {
    const deviceInfo = await getDefaultDeviceInfo()
    const res = await _ajax.post(_api.get_virtual_device, {
      device_id: deviceInfo?.id,
    })
    if (res.data?.code === 200) {
      setCurVirtualInfo(res.data.data)
    }
  }

  return (
    <>
      <div className="flex items-center justify-center gap-10 h-full w-full flex-col pt-12">
        <CommonNavBar
          title={t('详情')}
          onBack={() => {
            tabbarVisible({ visible: 1 })
            tabbarTo({ index: 'mine' })
            return true
          }}
        />
        <div className={classNames('flex-1 flex items-center')}>
          <MainImage curImg={curImg} className="h-full max-h-[66dvh]" />
        </div>
        <div className="w-full px-[20px] py-[12px] pb-[72px] flex gap-[12px]">
          <Button
            size="lg"
            className="maze-bg-gradient-btn flex-1 h-[56px] text-[16px] rounded-[150px] border-white border-[1px]"
            onClick={() => handleToolbarClick('download')}
          >
            <SvgIcon
              className="w-[24px] mr-[10px]"
              src={publicPreLoadSourceObj.detailDownload}
            />
            {t('下载')}
          </Button>
          {/* <Button
            size="lg"
            className="w-[56px] h-[56px] rounded-full bg-transparent border-white border-[1px]"
            onClick={() => handleToolbarClick('share')}
          >
            <SvgIcon src={publicPreLoadSourceObj.detailShare} alt="分享" />
          </Button> */}
          <Button
            size="lg"
            className="w-[56px] h-[56px] rounded-full bg-transparent border-white border-[1px]"
            onClick={() => {
              tabbarVisible({ visible: 1 })
              tabbarTo({ index: 'home' })
              navigate('/')
            }}
          >
            <SvgIcon src={publicPreLoadSourceObj.shootRetry} />
          </Button>
        </div>
      </div>
      <SendToEmailModal
        open={curOptType === 'email'}
        setOpen={() => setCurOptType(null)}
        imgId={null}
      />
      <DownloadModal
        open={curOptType === 'download'}
        setOpen={() => setCurOptType(null)}
        mazeImgUrl={
          curVirtualInfo?.status === 1 &&
          curVirtualInfo?.expire_ts * 1000 > +new Date()
            ? `${window.location.href}&virtual_uid=${curVirtualInfo?.uid}&navigate=noop`
            : curVideo
              ? curVideo.resultUrl
              : curImg?.resultUrl
        }
      />
      <PrintModal
        open={curOptType === 'print'}
        setOpen={() => setCurOptType(null)}
        curImg={curImg}
        mazeImgUrl={curImg?.resultUrl || ''}
      />
      <ShareModal
        open={curOptType === 'share'}
        setOpen={() => setCurOptType(null)}
        mazeImgUrl={curImg?.resultUrl || ''}
      />
    </>
  )
}
